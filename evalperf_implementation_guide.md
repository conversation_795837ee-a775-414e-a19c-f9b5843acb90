# EvalPerf性能测试框架实施指南

## 📋 项目概述

基于您的需求，我们已经成功实现了EvalPerf数据集与现有性能测试框架的集成方案。该方案能够：

1. **自动识别通过正确性测试的代码**：从现有的模型生成结果中筛选
2. **运行EvalPerf性能测试**：使用pe_input进行专门的性能测试
3. **与canonical solution对比**：使用性能最佳的参考实现作为基准
4. **批次统计分析**：支持多次迭代的统计学分析

## 🎯 核心发现

### Canonical Solution选择策略
- **EvalPerf分数含义**：分数越高表示性能越好
- **最佳选择**：使用最高分数的Reference作为canonical solution
- **验证结果**：HumanEval/9任务中，canonical score为100.0，性能差异达158.5倍

### 数据映射情况
- **EvalPerf总任务**：118个（29个HumanEval + 89个MBPP）
- **可测试的CodeLlama HumanEval任务**：24个
- **数据完整性**：所有任务都有对应的pe_input和多个reference实现

## 🏗️ 实施架构

### 1. 数据层
```
evalperf_processed/
├── compatible_format/
│   ├── evalperf_humaneval_compatible.jsonl  # 29个HumanEval任务
│   ├── evalperf_mbpp_compatible.jsonl       # 89个MBPP任务
│   └── evalperf_unified.jsonl               # 118个统一格式任务
```

每个任务包含：
- `canonical_solution`: 性能最佳的实现（最高分数）
- `canonical_score`: 最优性能分数
- `performance_baselines`: 多层次性能基线（best/median/worst）
- `pe_input`: 专门的性能测试数据

### 2. 测试框架
```python
class EvalPerfTester:
    def test_model_on_evalperf(self, model, output_dir, iterations=20):
        # 1. 加载EvalPerf数据集
        # 2. 遍历所有任务
        # 3. 检查是否有对应的生成代码（通过正确性测试）
        # 4. 运行性能测试（生成代码 vs canonical solution）
        # 5. 批次统计分析
        # 6. 生成对比报告
```

### 3. 性能测试指标
- **运行时间**：使用pe_input进行单次运行测试
- **内存使用**：tracemalloc监控峰值内存
- **CPU使用率**：psutil监控CPU占用
- **批次统计**：20次迭代，计算均值、标准差、置信区间

## 🔧 使用方法

### 基础测试
```bash
# 测试框架验证
python3 test_evalperf_framework.py

# 运行CodeLlama性能测试
python3 evalperf_performance_tester.py
```

### 自定义测试
```python
from evalperf_performance_tester import EvalPerfTester

# 创建测试器
tester = EvalPerfTester(
    evalperf_dataset_path="/path/to/evalperf_dataset.jsonl",
    generated_code_base_path="prompt/get-prompt-generated-code"
)

# 测试特定模型
results = tester.test_model_on_evalperf(
    model='codellama',
    output_dir='evalperf_results',
    iterations=20
)
```

## 📊 测试结果格式

```json
{
  "model": "codellama",
  "total_tasks": 29,
  "tested_tasks": 24,
  "successful_tasks": 20,
  "task_results": {
    "HumanEval/9": {
      "task_id": "HumanEval/9",
      "dataset": "humaneval",
      "entry_point": "rolling_max",
      "best_reference_score": 100.0,
      "generated_results": {
        "runtime_mean": 0.0023,
        "runtime_std": 0.0001,
        "memory_mean": 1024,
        "memory_std": 50,
        "success_count": 20
      },
      "reference_results": {
        "runtime_mean": 0.0015,
        "runtime_std": 0.0001,
        "memory_mean": 800,
        "memory_std": 30,
        "success_count": 20
      },
      "performance_ratio": {
        "runtime_ratio": 1.53,
        "memory_ratio": 1.28,
        "cpu_ratio": 1.12
      }
    }
  }
}
```

## 🎯 核心优势

### 1. 严格的性能基准
- 使用EvalPerf中性能最佳的实现作为基准
- 确保对比的公平性和科学性

### 2. 完整的测试流程
- 自动筛选通过正确性测试的代码
- 专门针对性能敏感场景的测试
- 多维度性能指标评估

### 3. 统计学可靠性
- 20次批次测试，消除随机误差
- 计算均值、标准差、置信区间
- 支持不同规模的性能测试数据

### 4. 框架兼容性
- 完全兼容现有的replication-final框架
- 可扩展到其他模型（DeepSeek、Copilot等）
- 支持HumanEval和MBPP两个数据集

## 📈 扩展计划

### 1. 多模型支持
```bash
# 扩展到其他模型
python3 evalperf_performance_tester.py --model deepseek
python3 evalperf_performance_tester.py --model copilot
```

### 2. COT Prompt对比
```python
# 实现COT格式的prompt测试
def test_cot_vs_standard_prompt():
    # 对比标准prompt和COT prompt的性能差异
```

### 3. 多规模测试
```python
# 支持不同规模的pe_input测试
pe_input_scales = ['small', 'medium', 'large', 'full']
```

## 🚀 下一步行动

1. **验证框架**：运行`test_evalperf_framework.py`确认环境配置
2. **执行测试**：运行`evalperf_performance_tester.py`获得初步结果
3. **分析结果**：基于测试结果调整参数和优化策略
4. **扩展模型**：将框架扩展到DeepSeek和Copilot模型
5. **统计分析**：实现更深入的统计学分析和可视化

## 📝 总结

该实施方案成功解决了您提出的核心需求：

✅ **正确理解EvalPerf分数**：分数越高性能越好  
✅ **基于现有生成结果**：利用已通过正确性测试的代码  
✅ **专门的性能测试**：使用pe_input进行单次性能测试  
✅ **科学的基准对比**：与最佳canonical solution对比  
✅ **批次统计分析**：20次迭代确保统计学意义  
✅ **框架完整性**：完全兼容现有测试框架  

现在可以开始实际的性能测试和数据收集工作了！
