import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages
import re

def read_data(file_path):
    data = {}
    with open(file_path, 'r') as file:
        for line in file:
            index, value = line.strip().split(':')
            data[int(index)] = float(value)
    return data

time_data1 = read_data('/home/<USER>/experiment/copilot/EMSE/Humaneval-replication/result/Execution-Time/numbered_answers_humaneval_30_cprofile-no1.txt')
time_data2 = read_data('/home/<USER>/experiment/copilot/EMSE/Humaneval-replication/result/Execution-Time/numbered_humaneval_30_cprofile-no1.txt')
# time_data1 = read_data('./results_for_humaneval/Execution-Time/numbered_answers_humaneval_30_cprofile_all.txt')
# time_data2 = read_data('./results_for_humaneval/Execution-Time/numbered_humaneval_30_cprofile.txt')

common_indices = set(time_data1.keys()) & set(time_data2.keys())
array_time1 = [time_data1[i] for i in sorted(common_indices)]
array_time2 = [time_data2[i] for i in sorted(common_indices)]

def read_profile_data(filepath):
    data = {}
    with open(filepath, 'r') as file:
        for line in file:
            parts = line.split(':')
            if len(parts) == 2:
                filename = parts[0].strip()
                time = float(parts[1].replace('MB', '').strip())
                data[filename] = time
    return data

def extract_number(filename):
    match = re.search(r'\d+', filename)
    return int(match.group()) if match else None

# data1 = read_profile_data('/home/<USER>/experiment/copilot/EMSE/Humaneval-replication/result/Memory/Answers_HumanEval_30_memory.txt')
# data2 = read_profile_data('/home/<USER>/experiment/copilot/EMSE/Humaneval-replication/result/Memory/HumanEval_30_memory.txt')
data1 = read_profile_data('./results_for_humaneval/Memory/Answers_HumanEval_30_memory_all.txt')
data2 = read_profile_data('./results_for_humaneval/Memory/HumanEval_30_memory.txt')

filenames = sorted(set(data1.keys()) & set(data2.keys()), key=extract_number)
array_memory1 = [data1[name] for name in filenames]
array_memory2 = [data2[name] for name in filenames]

def parse_data(filename):
    cpu_usages = {}
    with open(filename, 'r') as file:
        for line in file:
            cpu_match = re.search(r'CPU Usages = \[(\d+\.\d+)\]', line)
            index_match = re.search(r'enc_(\d+)', line)
            if cpu_match and index_match:
                index = int(index_match.group(1))
                cpu_usage = float(cpu_match.group(1))
                cpu_usages[index] = cpu_usage
    sorted_cpu_usages = [cpu_usages[i] for i in sorted(cpu_usages.keys())]
    return sorted_cpu_usages

# array_cpu1 = parse_data('/home/<USER>/experiment/copilot/EMSE/Humaneval-replication/result/CPU/Answers_Humaneval_30_cpu_usages.txt')
# array_cpu2 = parse_data('/home/<USER>/experiment/copilot/EMSE/Humaneval-replication/result/CPU/Humaneval_30_cpu_usages.txt')
# array_cpu1 = parse_data('./results_for_humaneval/CPU/Answers_Humaneval_30_cpu_usages_all.txt')
array_cpu2 = parse_data('./results_for_humaneval/CPU/Humaneval_30_cpu_usages.txt')
array_cpu1 = parse_data('/home/<USER>/experiment/copilot/EMSE/Humaneval-replication/new-test-for-emse/Anwsers_cpu_134.txt')
# array_cpu2 = parse_data('/home/<USER>/experiment/copilot/EMSE/Humaneval-replication/new-test-for-emse/cpu3.txt')

# Function to plot and save execution times
def plot_and_save_times(times1, times2, title, output_file):
    plt.figure(figsize=(10, 7))
    plt.plot(times1, label='Canonical Code')
    plt.plot(times2, label='Generated Code by Copilot')
    plt.legend(fontsize=15)
    plt.xlabel('File Index', fontsize=15)
    plt.ylabel('Cumulative Time (Seconds)', fontsize=15)
    # plt.title(title, fontsize=16)
    # plt.subplots_adjust(bottom=0.5)
    # plt.figtext(0.5, -0.12, title, ha="center", fontsize=17)
    plt.subplots_adjust(bottom=0.2)  # Adjust bottom margin to make room for title
    plt.figtext(0.5, 0.06, title, ha='center', fontsize=17)
    output_file.savefig(bbox_inches='tight', pad_inches=0.5)
    plt.close()  # Close the current figure to prevent overlapping

# Function to plot and save memory usage
def plot_and_save_memory(array_memory1, array_memory2, title, output_file):
    plt.figure(figsize=(10, 7))
    plt.plot(array_memory1, label='Canonical Code')
    plt.plot(array_memory2, label='Generated Code by Copilot')
    plt.xlabel('File Index', fontsize=15)
    plt.ylabel('Memory Usage (MB)', fontsize=15)
    # plt.title(title, fontsize=16)
    # plt.subplots_adjust(bottom=0.5)
    # plt.figtext(0.5, -0.12, title, ha="center", fontsize=17)
    plt.subplots_adjust(bottom=0.2)  # Adjust bottom margin to make room for title
    plt.figtext(0.5, 0.06, title, ha='center', fontsize=17)
    plt.legend(fontsize=15)
    output_file.savefig(bbox_inches='tight', pad_inches=0.5)
    plt.close()  # Close the current figure to prevent overlapping

# Function to plot and save CPU usage
def plot_and_save_cpu(array_cpu1, array_cpu2, title, output_file):
    plt.figure(figsize=(10, 7))
    plt.plot(array_cpu1, label='Canonical Code')
    plt.plot(array_cpu2, label='Generated Code by Copilot')
    plt.xlabel('File Index', fontsize=15)
    plt.ylabel('CPU Usage(%)', fontsize=15)
    # plt.title(title, fontsize=16)
    # plt.subplots_adjust(bottom=0.5)
    # plt.figtext(0.5, -0.12, title, ha="center", fontsize=17)
    plt.subplots_adjust(bottom=0.2)  # Adjust bottom margin to make room for title
    plt.figtext(0.5, 0.06, title, ha='center', fontsize=17)
    plt.legend(fontsize=15)
    output_file.savefig(bbox_inches='tight', pad_inches=0.5)
    plt.close()  # Close the current figure to prevent overlapping

# Open PDF file to save the plots
with PdfPages('./combined_plots.pdf') as pdf:

    # Plot and save execution times
    plot_and_save_times(array_time1, array_time2, "Execution Time", pdf)
    
    # Plot and save memory usage
    plot_and_save_memory(array_memory1, array_memory2, "Memory Usage", pdf)
    
    # Plot and save CPU usage
    plot_and_save_cpu(array_cpu1, array_cpu2, "CPU Utilization", pdf)

print("All plots have been saved to combined_plots.pdf")
