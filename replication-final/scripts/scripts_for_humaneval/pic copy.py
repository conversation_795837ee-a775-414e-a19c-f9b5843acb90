import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages
import re

def read_data(file_path):
    data = {}
    with open(file_path, 'r') as file:
        for line in file:
            index, value = line.strip().split(':')
            data[int(index)] = float(value)
    return data

time_data1 = read_data('/home/<USER>/experiment/copilot/EMSE/Humaneval-replication/result/Execution-Time/numbered_answers_humaneval_30_cprofile-no1.txt')
time_data2 = read_data('/home/<USER>/experiment/copilot/EMSE/Humaneval-replication/result/Execution-Time/numbered_humaneval_30_cprofile-no1.txt')
# time_data1 = read_data('./results_for_humaneval/Execution-Time/numbered_answers_humaneval_30_cprofile_all.txt')
# time_data2 = read_data('./results_for_humaneval/Execution-Time/numbered_humaneval_30_cprofile.txt')

common_indices = set(time_data1.keys()) & set(time_data2.keys())
array_time1 = [time_data1[i] for i in sorted(common_indices)]
array_time2 = [time_data2[i] for i in sorted(common_indices)]

def read_profile_data(filepath):
    data = {}
    with open(filepath, 'r') as file:
        for line in file:
            parts = line.split(':')
            if len(parts) == 2:
                filename = parts[0].strip()
                time = float(parts[1].replace('MB', '').strip())
                data[filename] = time
    return data

def extract_number(filename):
    match = re.search(r'\d+', filename)
    return int(match.group()) if match else None

# data1 = read_profile_data('/home/<USER>/experiment/copilot/EMSE/Humaneval-replication/result/Memory/Answers_HumanEval_30_memory.txt')
# data2 = read_profile_data('/home/<USER>/experiment/copilot/EMSE/Humaneval-replication/result/Memory/HumanEval_30_memory.txt')
data1 = read_profile_data('./results_for_humaneval/Memory/Answers_HumanEval_30_memory_all.txt')
data2 = read_profile_data('./results_for_humaneval/Memory/HumanEval_30_memory.txt')

filenames = sorted(set(data1.keys()) & set(data2.keys()), key=extract_number)
array_memory1 = [data1[name] for name in filenames]
array_memory2 = [data2[name] for name in filenames]

def parse_data(filename):
    cpu_usages = {}
    with open(filename, 'r') as file:
        for line in file:
            cpu_match = re.search(r'CPU Usages = \[(\d+\.\d+)\]', line)
            index_match = re.search(r'enc_(\d+)', line)
            if cpu_match and index_match:
                index = int(index_match.group(1))
                cpu_usage = float(cpu_match.group(1))
                cpu_usages[index] = cpu_usage
    sorted_cpu_usages = [cpu_usages[i] for i in sorted(cpu_usages.keys())]
    return sorted_cpu_usages

# array_cpu1 = parse_data('/home/<USER>/experiment/copilot/EMSE/Humaneval-replication/result/CPU/Answers_Humaneval_30_cpu_usages.txt')
# array_cpu2 = parse_data('/home/<USER>/experiment/copilot/EMSE/Humaneval-replication/result/CPU/Humaneval_30_cpu_usages.txt')
# array_cpu1 = parse_data('./results_for_humaneval/CPU/Answers_Humaneval_30_cpu_usages_all.txt')
array_cpu2 = parse_data('./results_for_humaneval/CPU/Humaneval_30_cpu_usages.txt')
array_cpu1 = parse_data('/home/<USER>/experiment/copilot/EMSE/Humaneval-replication/new-test-for-emse/Anwsers_cpu_134.txt')
# array_cpu2 = parse_data('/home/<USER>/experiment/copilot/EMSE/Humaneval-replication/new-test-for-emse/cpu3.txt')

with PdfPages('./combined_plots3.pdf') as pdf:
    # Create figure with more vertical space
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(7, 15))
    
    # Execution Time Plot
    ax1.plot(array_time1, label='Canonical Code')
    ax1.plot(array_time2, label='Generated Code by Copilot')
    ax1.set_xlabel('File Index', fontsize=15)
    ax1.set_ylabel('Cumulative Time (Seconds)', fontsize=15)
    ax1.legend(fontsize=15)
    plt.subplots_adjust(bottom=0.2)
    ax1.text(0.5, -0.15, 'Execution Time', 
             horizontalalignment='center', 
             verticalalignment='center', 
             transform=ax1.transAxes, 
             fontsize=17)

    # Memory Usage Plot
    ax2.plot(array_memory1, label='Canonical Code')
    ax2.plot(array_memory2, label='Generated Code by Copilot')
    ax2.set_xlabel('File Index', fontsize=15)
    ax2.set_ylabel('Memory Usage (MB)', fontsize=15)
    ax2.legend(fontsize=15)

    ax2.text(0.5, -0.15, 'Memory Usage', 
             horizontalalignment='center', 
             verticalalignment='center', 
             transform=ax2.transAxes, 
             fontsize=17)

    # CPU Usage Plot
    ax3.plot(array_cpu1, label='Canonical Code')
    ax3.plot(array_cpu2, label='Generated Code by Copilot')
    ax3.set_xlabel('File Index', fontsize=15)
    ax3.set_ylabel('CPU Usage (%)', fontsize=15)
    ax3.legend(fontsize=15)
    ax3.text(0.5, -0.15, 'CPU Utilization', 
             horizontalalignment='center', 
             verticalalignment='center', 
             transform=ax3.transAxes, 
             fontsize=17)

    # Adjust layout to prevent overlap
    plt.tight_layout(h_pad=2.0, rect=[0, 0.03, 1, 0.95])

    # Save the figure
    pdf.savefig(fig, bbox_inches='tight', pad_inches=0.5)
    plt.close(fig)

print("All plots have been saved in a single page of combined_plots.pdf")