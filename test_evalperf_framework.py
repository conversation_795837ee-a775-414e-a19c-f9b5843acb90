#!/usr/bin/env python3
"""
测试EvalPerf性能测试框架
"""

import json
import os
from evalperf_performance_tester import EvalPerfTester


def test_single_task():
    """测试单个任务"""
    print("=== 测试单个EvalPerf任务 ===")
    
    # 配置路径
    evalperf_dataset_path = "/home/<USER>/experiment/copilot/EMSE/25-7/EvalPerf/evalperf_dataset.jsonl"
    generated_code_base_path = "prompt/get-prompt-generated-code"
    
    # 创建测试器
    tester = EvalPerfTester(evalperf_dataset_path, generated_code_base_path)
    
    # 测试HumanEval/9任务
    task_id = "HumanEval/9"
    
    print(f"测试任务: {task_id}")
    
    # 检查EvalPerf数据
    if task_id in tester.evalperf_data:
        evalperf_item = tester.evalperf_data[task_id]
        print(f"Entry point: {evalperf_item['entry_point']}")
        print(f"Reference数量: {len(evalperf_item['reference'])}")
        print(f"Scores: {[round(s, 2) for s in evalperf_item['scores']]}")
        
        # 获取最佳参考解决方案
        best_reference, best_score = tester._get_best_reference_solution(task_id)
        print(f"最佳参考解决方案分数: {best_score}")
        print(f"最佳参考解决方案代码长度: {len(best_reference)} 字符")
        
        # 准备PE输入数据
        pe_input = tester._prepare_pe_input(evalperf_item['pe_input'])
        print(f"PE输入数据类型: {type(pe_input)}")
        if isinstance(pe_input, list):
            print(f"PE输入数据长度: {len(pe_input)}")
        
        # 检查生成的代码
        generated_code = tester._load_generated_code('codellama', 'humaneval', task_id)
        if generated_code:
            print(f"✓ 找到CodeLlama生成的代码")
            print(f"生成代码长度: {len(generated_code)} 字符")
            
            # 提取函数
            function_code = tester._extract_function_from_code(generated_code, evalperf_item['entry_point'])
            print(f"提取的函数代码长度: {len(function_code)} 字符")
            print("提取的函数代码:")
            print(function_code[:200] + "..." if len(function_code) > 200 else function_code)
        else:
            print("✗ 未找到CodeLlama生成的代码")
    else:
        print(f"✗ 任务 {task_id} 不在EvalPerf数据集中")


def test_framework_structure():
    """测试框架结构"""
    print("\n=== 测试框架结构 ===")
    
    # 检查数据集文件
    evalperf_dataset_path = "/home/<USER>/experiment/copilot/EMSE/25-7/EvalPerf/evalperf_dataset.jsonl"
    if os.path.exists(evalperf_dataset_path):
        print("✓ EvalPerf数据集文件存在")
    else:
        print("✗ EvalPerf数据集文件不存在")
    
    # 检查生成代码目录
    generated_code_paths = [
        "prompt/get-prompt-generated-code/codellama/humaneval/HumanEval-infilling-correct",
        "prompt/get-prompt-generated-code/codellama/humaneval/HumanEval-infilling",
        "prompt/get-prompt-generated-code/deepseek/humaneval",
        "prompt/get-prompt-generated-code/copilot/humaneval"
    ]
    
    for path in generated_code_paths:
        if os.path.exists(path):
            file_count = len([f for f in os.listdir(path) if f.endswith('.py')])
            print(f"✓ {path} 存在 ({file_count} 个Python文件)")
        else:
            print(f"✗ {path} 不存在")


def test_evalperf_data_mapping():
    """测试EvalPerf数据映射"""
    print("\n=== 测试EvalPerf数据映射 ===")
    
    evalperf_dataset_path = "/home/<USER>/experiment/copilot/EMSE/25-7/EvalPerf/evalperf_dataset.jsonl"
    generated_code_base_path = "prompt/get-prompt-generated-code"
    
    tester = EvalPerfTester(evalperf_dataset_path, generated_code_base_path)
    
    # 统计可测试的任务
    humaneval_tasks = []
    mbpp_tasks = []
    available_codellama_humaneval = []
    
    for task_id in tester.evalperf_data.keys():
        if task_id.startswith('HumanEval/'):
            humaneval_tasks.append(task_id)
            # 检查是否有对应的生成代码
            if tester._load_generated_code('codellama', 'humaneval', task_id):
                available_codellama_humaneval.append(task_id)
        elif task_id.startswith('Mbpp/'):
            mbpp_tasks.append(task_id)
    
    print(f"EvalPerf中的HumanEval任务: {len(humaneval_tasks)}")
    print(f"EvalPerf中的MBPP任务: {len(mbpp_tasks)}")
    print(f"有CodeLlama生成代码的HumanEval任务: {len(available_codellama_humaneval)}")
    
    if len(available_codellama_humaneval) > 0:
        print(f"可测试的任务示例: {available_codellama_humaneval[:5]}")
    
    # 检查具体的任务映射
    test_tasks = ["HumanEval/0", "HumanEval/9", "HumanEval/16", "HumanEval/20"]
    for task_id in test_tasks:
        if task_id in tester.evalperf_data:
            has_code = tester._load_generated_code('codellama', 'humaneval', task_id) is not None
            print(f"{task_id}: EvalPerf数据 ✓, CodeLlama代码 {'✓' if has_code else '✗'}")


def main():
    """主函数"""
    print("EvalPerf性能测试框架验证")
    print("=" * 50)
    
    test_framework_structure()
    test_evalperf_data_mapping()
    test_single_task()
    
    print("\n" + "=" * 50)
    print("测试完成！")


if __name__ == "__main__":
    main()
