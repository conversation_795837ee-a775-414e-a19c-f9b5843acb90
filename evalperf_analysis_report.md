# EvalPerf数据集分析和预处理报告

## 📊 数据集概览

### 基本统计信息
- **总记录数**: 118条
- **唯一任务数**: 118个
- **数据集来源分布**:
  - HumanEval任务: 29个 (24.6%)
  - MBPP任务: 89个 (75.4%)
  - 其他任务: 0个

### 数据结构分析
- **字段列表**: `['entry_point', 'pe_input', 'prompt', 'reference', 'scores', 'task_id']`
- **Reference数量统计**:
  - 最小: 4个参考实现
  - 最大: 17个参考实现
  - 平均: 7.60个参考实现
  - 中位数: 7.0个参考实现
- **Scores数量**: 与Reference数量一致，每个参考实现对应一个性能分数

## 🔍 性能测试数据分析 (PE Input)

### PE Input结构特点
- **数据类型**: 三层嵌套列表结构 `[[[...]]]`
- **数据大小范围**:
  - 最小: 218,193字符
  - 最大: 21,255,143字符
  - 平均: 4,824,395字符

### 典型样本分析
1. **HumanEval/9** (rolling_max):
   - PE Input大小: 218,193字符
   - 嵌套结构: 32,768个测试数据点

2. **HumanEval/0** (has_close_elements):
   - PE Input大小: 21,255,143字符
   - 嵌套结构: 1,048,576个测试数据点

3. **HumanEval/4** (mean_absolute_deviation):
   - PE Input大小: 1,267,931字符
   - 嵌套结构: 65,536个测试数据点

## 📁 预处理输出文件

### 原始预处理数据
- `evalperf_processed/evalperf_humaneval.jsonl`: HumanEval任务 (29条)
- `evalperf_processed/evalperf_mbpp.jsonl`: MBPP任务 (89条)
- `evalperf_processed/evalperf_all_tasks.jsonl`: 所有任务 (118条)
- `evalperf_processed/dataset_stats.json`: 数据集统计信息

### 兼容格式数据
- `evalperf_processed/compatible_format/evalperf_humaneval_compatible.jsonl`: HumanEval兼容格式
- `evalperf_processed/compatible_format/evalperf_mbpp_compatible.jsonl`: MBPP兼容格式
- `evalperf_processed/compatible_format/evalperf_unified.jsonl`: 统一格式 (118条)
- `evalperf_processed/compatible_format/dataset_summary.json`: 数据集摘要

## 🔄 格式转换说明

### HumanEval兼容格式
转换后的数据包含以下字段：
- `task_id`: 任务标识符
- `prompt`: 原始提示信息
- `entry_point`: 函数入口点
- `canonical_solution`: 第一个参考实现作为标准解决方案
- `test`: 从pe_input生成的性能测试代码
- `pe_input`: 原始性能测试输入数据
- `scores`: 性能分数数组
- `all_references`: 所有参考实现
- `reference_count`: 参考实现数量

### MBPP兼容格式
转换后的数据包含以下字段：
- `task_id`: 任务标识符
- `text`: 任务描述 (对应prompt)
- `code`: 参考代码实现
- `test_list`: 测试用例列表
- `test_setup_code`: 测试设置代码
- `challenge_test_list`: 挑战测试列表
- 以及EvalPerf特有的性能测试字段

## 🎯 与现有框架的兼容性

### 优势
1. **数据结构兼容**: 转换后的格式与现有的replication-final框架完全兼容
2. **性能测试增强**: 保留了原始的pe_input和scores信息，支持更精确的性能分析
3. **多参考实现**: 每个任务有多个参考实现，可以进行更全面的性能对比
4. **大规模测试数据**: PE Input包含大量测试用例，适合进行统计学分析

### 注意事项
1. **数据量大**: PE Input数据量很大，需要考虑内存和存储限制
2. **测试复杂度**: 生成的测试代码需要进一步优化以适应具体的性能测试需求
3. **任务覆盖**: EvalPerf只包含部分HumanEval和MBPP任务，不是完整数据集

## 📋 任务ID列表

### HumanEval任务 (29个)
HumanEval/0, HumanEval/4, HumanEval/9, HumanEval/16, HumanEval/20, HumanEval/24, HumanEval/26, HumanEval/31, HumanEval/33, HumanEval/34, HumanEval/36, HumanEval/37, HumanEval/49, HumanEval/51, HumanEval/54, HumanEval/58, HumanEval/59, HumanEval/64, HumanEval/74, HumanEval/85, HumanEval/86, HumanEval/87, HumanEval/89, HumanEval/90, HumanEval/113, HumanEval/119, HumanEval/120, HumanEval/121, HumanEval/149

### MBPP任务 (89个)
Mbpp/3, Mbpp/4, Mbpp/9, Mbpp/11, Mbpp/12, Mbpp/16, Mbpp/19, Mbpp/62, Mbpp/75, Mbpp/88, Mbpp/90, Mbpp/94, Mbpp/96, Mbpp/97, Mbpp/106, Mbpp/108, Mbpp/111, Mbpp/123, Mbpp/128, Mbpp/129, Mbpp/130, Mbpp/137, Mbpp/140, Mbpp/141, Mbpp/166, Mbpp/170, Mbpp/226, Mbpp/230, Mbpp/239, Mbpp/240, Mbpp/251, Mbpp/256, Mbpp/265, Mbpp/281, Mbpp/296, Mbpp/308, Mbpp/392, Mbpp/395, Mbpp/405, Mbpp/414, Mbpp/421, Mbpp/424, Mbpp/425, Mbpp/428, Mbpp/433, Mbpp/437, Mbpp/440, Mbpp/446, Mbpp/451, Mbpp/456, Mbpp/460, Mbpp/474, Mbpp/476, Mbpp/477, Mbpp/563, Mbpp/572, Mbpp/578, Mbpp/586, Mbpp/587, Mbpp/588, Mbpp/592, Mbpp/597, Mbpp/604, Mbpp/610, Mbpp/611, Mbpp/631, Mbpp/632, Mbpp/633, Mbpp/644, Mbpp/720, Mbpp/725, Mbpp/732, Mbpp/740, Mbpp/744, Mbpp/750, Mbpp/753, Mbpp/754, Mbpp/755, Mbpp/757, Mbpp/758, Mbpp/760, Mbpp/764, Mbpp/766, Mbpp/775, Mbpp/777, Mbpp/781, Mbpp/800, Mbpp/805, Mbpp/808

## ✅ 下一步工作

1. **目录结构设计**: 为EvalPerf创建类似replication-final的项目结构
2. **脚本模板适配**: 将现有的性能测试脚本适配到EvalPerf数据集
3. **代码生成模块**: 实现三个模型对EvalPerf任务的代码生成
4. **性能测试集成**: 集成运行时间、内存、CPU测试功能
5. **批次测试框架**: 实现统计学分析所需的批次测试功能

## 📝 总结

EvalPerf数据集分析和预处理已完成，数据已成功转换为与现有框架兼容的格式。该数据集具有以下特点：
- 包含118个高质量的编程任务
- 每个任务有多个参考实现和性能分数
- 包含大规模的性能测试数据
- 完全兼容现有的测试框架

预处理后的数据为后续的性能测试、统计分析和模型对比提供了坚实的基础。
