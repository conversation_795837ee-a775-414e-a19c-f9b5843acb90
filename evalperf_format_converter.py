#!/usr/bin/env python3
"""
EvalPerf数据格式转换脚本
将EvalPerf数据集转换为与现有replication-final框架兼容的格式
"""

import json
import os
from typing import Dict, List, Any

class EvalPerfFormatConverter:
    def __init__(self, processed_data_dir: str):
        self.processed_data_dir = processed_data_dir
        
    def convert_to_humaneval_format(self, input_file: str, output_file: str):
        """将EvalPerf数据转换为HumanEval格式"""
        print(f"转换 {input_file} 到 HumanEval 格式...")

        converted_data = []

        with open(input_file, 'r', encoding='utf-8') as f:
            for line in f:
                evalperf_item = json.loads(line.strip())

                # 选择性能最佳的reference作为canonical solution
                best_solution_info = self._select_best_reference(
                    evalperf_item["references"],
                    evalperf_item["scores"]
                )

                # 转换为HumanEval格式
                humaneval_item = {
                    "task_id": evalperf_item["task_id"],
                    "prompt": evalperf_item["prompt"],
                    "entry_point": evalperf_item["entry_point"],
                    "canonical_solution": best_solution_info["solution"],
                    "canonical_score": best_solution_info["score"],
                    "canonical_rank": best_solution_info["rank"],
                    "test": self._generate_test_from_pe_input(evalperf_item),
                    # EvalPerf特有字段
                    "pe_input": evalperf_item["pe_input"],
                    "scores": evalperf_item["scores"],
                    "all_references": evalperf_item["references"],
                    "reference_count": evalperf_item["reference_count"],
                    "performance_baselines": self._generate_performance_baselines(
                        evalperf_item["references"],
                        evalperf_item["scores"]
                    )
                }

                converted_data.append(humaneval_item)
        
        # 保存转换后的数据
        with open(output_file, 'w', encoding='utf-8') as f:
            for item in converted_data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        print(f"转换完成，保存到: {output_file} ({len(converted_data)}条)")
        return converted_data
    
    def convert_to_mbpp_format(self, input_file: str, output_file: str):
        """将EvalPerf数据转换为MBPP格式"""
        print(f"转换 {input_file} 到 MBPP 格式...")

        converted_data = []

        with open(input_file, 'r', encoding='utf-8') as f:
            for line in f:
                evalperf_item = json.loads(line.strip())

                # 选择性能最佳的reference作为canonical solution
                best_solution_info = self._select_best_reference(
                    evalperf_item["references"],
                    evalperf_item["scores"]
                )

                # 转换为MBPP格式
                mbpp_item = {
                    "task_id": evalperf_item["task_id"],
                    "text": evalperf_item["prompt"],
                    "code": best_solution_info["solution"],
                    "canonical_score": best_solution_info["score"],
                    "canonical_rank": best_solution_info["rank"],
                    "test_list": [self._generate_test_from_pe_input(evalperf_item)],
                    "test_setup_code": "",
                    "challenge_test_list": [],
                    # EvalPerf特有字段
                    "pe_input": evalperf_item["pe_input"],
                    "scores": evalperf_item["scores"],
                    "all_references": evalperf_item["references"],
                    "reference_count": evalperf_item["reference_count"],
                    "entry_point": evalperf_item["entry_point"],
                    "performance_baselines": self._generate_performance_baselines(
                        evalperf_item["references"],
                        evalperf_item["scores"]
                    )
                }

                converted_data.append(mbpp_item)
        
        # 保存转换后的数据
        with open(output_file, 'w', encoding='utf-8') as f:
            for item in converted_data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        print(f"转换完成，保存到: {output_file} ({len(converted_data)}条)")
        return converted_data

    def _select_best_reference(self, references: List[str], scores: List[float]) -> Dict[str, Any]:
        """选择性能最佳的reference（分数越高性能越好）"""
        best_idx = scores.index(max(scores))
        return {
            "solution": references[best_idx],
            "score": scores[best_idx],
            "rank": 1,
            "index": best_idx
        }

    def _generate_performance_baselines(self, references: List[str], scores: List[float]) -> Dict[str, Any]:
        """生成性能基线（分数越高性能越好）"""
        sorted_pairs = sorted(zip(references, scores), key=lambda x: x[1], reverse=True)
        return {
            "best": {"solution": sorted_pairs[0][0], "score": sorted_pairs[0][1]},
            "median": {"solution": sorted_pairs[len(sorted_pairs)//2][0], "score": sorted_pairs[len(sorted_pairs)//2][1]},
            "worst": {"solution": sorted_pairs[-1][0], "score": sorted_pairs[-1][1]},
            "score_range": {"min": sorted_pairs[-1][1], "max": sorted_pairs[0][1]},
            "performance_gap": sorted_pairs[0][1] / sorted_pairs[-1][1] if sorted_pairs[-1][1] > 0 else float('inf')
        }

    def _generate_test_from_pe_input(self, evalperf_item: Dict[str, Any]) -> str:
        """从pe_input生成测试代码"""
        entry_point = evalperf_item["entry_point"]
        pe_input = evalperf_item["pe_input"]

        # 解析pe_input
        try:
            pe_data = json.loads(pe_input) if isinstance(pe_input, str) else pe_input
        except (json.JSONDecodeError, TypeError):
            pe_data = pe_input
        
        # 生成基本的测试框架
        test_code = f"""
def check({entry_point}):
    # EvalPerf性能测试
    import json
    
    # 加载性能测试数据
    pe_input = {repr(pe_input)}
    pe_data = json.loads(pe_input) if isinstance(pe_input, str) else pe_input
    
    # 执行性能测试
    if isinstance(pe_data, list) and len(pe_data) > 0:
        test_cases = pe_data[0] if isinstance(pe_data[0], list) else pe_data
        
        for i, test_case in enumerate(test_cases[:10]):  # 限制测试用例数量
            try:
                if isinstance(test_case, list):
                    result = {entry_point}(test_case)
                else:
                    result = {entry_point}(test_case)
                # 验证结果不为None
                assert result is not None
            except Exception as e:
                print(f"Test case {{i}} failed: {{e}}")
                continue
    
    print("Performance test completed")

check({entry_point})
"""
        return test_code.strip()
    
    def create_compatible_dataset(self):
        """创建与现有框架兼容的数据集"""
        output_dir = os.path.join(self.processed_data_dir, "compatible_format")
        os.makedirs(output_dir, exist_ok=True)
        
        # 转换HumanEval数据
        humaneval_input = os.path.join(self.processed_data_dir, "evalperf_humaneval.jsonl")
        humaneval_output = os.path.join(output_dir, "evalperf_humaneval_compatible.jsonl")
        
        if os.path.exists(humaneval_input):
            self.convert_to_humaneval_format(humaneval_input, humaneval_output)
        
        # 转换MBPP数据
        mbpp_input = os.path.join(self.processed_data_dir, "evalperf_mbpp.jsonl")
        mbpp_output = os.path.join(output_dir, "evalperf_mbpp_compatible.jsonl")
        
        if os.path.exists(mbpp_input):
            self.convert_to_mbpp_format(mbpp_input, mbpp_output)
        
        # 创建统一的数据集文件
        self._create_unified_dataset(output_dir)
        
        print("\n=== 兼容格式转换完成 ===")
        print(f"兼容格式数据保存在: {output_dir}")

        return output_dir
    
    def _create_unified_dataset(self, output_dir: str):
        """创建统一的数据集文件"""
        humaneval_file = os.path.join(output_dir, "evalperf_humaneval_compatible.jsonl")
        mbpp_file = os.path.join(output_dir, "evalperf_mbpp_compatible.jsonl")
        unified_file = os.path.join(output_dir, "evalperf_unified.jsonl")
        
        all_data = []
        
        # 合并HumanEval数据
        if os.path.exists(humaneval_file):
            with open(humaneval_file, 'r', encoding='utf-8') as f:
                for line in f:
                    data = json.loads(line.strip())
                    data['dataset_type'] = 'HumanEval'
                    all_data.append(data)
        
        # 合并MBPP数据
        if os.path.exists(mbpp_file):
            with open(mbpp_file, 'r', encoding='utf-8') as f:
                for line in f:
                    data = json.loads(line.strip())
                    data['dataset_type'] = 'MBPP'
                    all_data.append(data)
        
        # 保存统一数据集
        with open(unified_file, 'w', encoding='utf-8') as f:
            for item in all_data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        print(f"统一数据集保存到: {unified_file} ({len(all_data)}条)")
        
        # 创建数据集摘要
        summary = {
            'total_tasks': len(all_data),
            'humaneval_tasks': sum(1 for item in all_data if item['dataset_type'] == 'HumanEval'),
            'mbpp_tasks': sum(1 for item in all_data if item['dataset_type'] == 'MBPP'),
            'task_ids': [item['task_id'] for item in all_data]
        }
        
        summary_file = os.path.join(output_dir, "dataset_summary.json")
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"数据集摘要保存到: {summary_file}")

def main():
    processed_data_dir = "./evalperf_processed"
    
    if not os.path.exists(processed_data_dir):
        print(f"错误: 预处理数据目录不存在: {processed_data_dir}")
        print("请先运行 evalperf_data_analysis.py 进行数据预处理")
        return
    
    # 创建格式转换器
    converter = EvalPerfFormatConverter(processed_data_dir)
    
    # 执行格式转换
    output_dir = converter.create_compatible_dataset()
    
    print("\n=== 格式转换完成 ===")
    print("转换后的数据可以直接用于现有的性能测试框架")
    print(f"输出目录: {output_dir}")

if __name__ == "__main__":
    main()
