# EvalPerf集成到性能测试框架的方案

## 🎯 关于Canonical Solution的选择

### 分析结果（已修正）
根据对EvalPerf数据集的深入分析，我们发现：

1. **Scores含义**: EvalPerf中的scores表示性能分数，**分数越高表示性能越好**
2. **Reference排序**: Reference按性能从劣到优排序，最后一个Reference通常是最优实现
3. **性能差异**: 不同Reference之间存在显著性能差异（158.5倍）

### 推荐方案
**建议使用性能最佳的Reference作为canonical solution**，具体策略：

```python
# 选择性能最佳的reference作为canonical solution
def get_canonical_solution(references, scores):
    best_idx = scores.index(max(scores))  # 找到最高分数的索引
    return references[best_idx]
```

### 理由
1. **性能导向**: EvalPerf专注于性能评估，使用最优性能的实现作为基准更合理
2. **公平对比**: 确保生成的代码与最佳实现进行对比，提供更严格的评估标准
3. **实际意义**: 最优实现代表了该任务的性能上限，有助于评估模型的优化能力

### 验证结果
以HumanEval/9为例：
- 所有scores: [0.63, 0.95, 4.73, 5.36, 63.09, 64.04, 64.98, 76.34, 100.0]
- Canonical Score: 100.0 (最高分，性能最佳)
- Performance Gap: 158.5x (最佳与最差的性能差异)

## 🏗️ EvalPerf集成到性能测试框架的方案

### 1. 数据层集成

#### 1.1 数据预处理增强
```python
class EvalPerfProcessor:
    def select_canonical_solution(self, references, scores):
        """选择性能最佳的reference作为canonical solution"""
        best_idx = scores.index(min(scores))
        return {
            'canonical_solution': references[best_idx],
            'canonical_score': scores[best_idx],
            'performance_rank': 1,
            'all_references': references,
            'all_scores': scores
        }
    
    def generate_performance_baselines(self, references, scores):
        """生成多层次性能基线"""
        sorted_pairs = sorted(zip(references, scores), key=lambda x: x[1])
        return {
            'best': sorted_pairs[0],      # 最优实现
            'median': sorted_pairs[len(sorted_pairs)//2],  # 中位数实现
            'worst': sorted_pairs[-1]     # 最差实现
        }
```

#### 1.2 测试数据优化
```python
def optimize_pe_input(pe_input_raw):
    """优化性能测试输入数据"""
    pe_data = json.loads(pe_input_raw) if isinstance(pe_input_raw, str) else pe_input_raw
    
    # 分层测试：小、中、大规模数据
    if isinstance(pe_data, list) and len(pe_data) > 0:
        test_cases = pe_data[0] if isinstance(pe_data[0], list) else pe_data
        return {
            'small': test_cases[:100],      # 小规模测试
            'medium': test_cases[:1000],    # 中规模测试  
            'large': test_cases[:10000],    # 大规模测试
            'full': test_cases              # 完整测试
        }
```

### 2. 框架层集成

#### 2.1 目录结构设计
```
replication-final/
├── data/
│   ├── humaneval-v2-20210705.jsonl
│   ├── mbpp.jsonl
│   └── evalperf/                    # 新增EvalPerf数据
│       ├── evalperf_humaneval.jsonl
│       ├── evalperf_mbpp.jsonl
│       └── evalperf_unified.jsonl
├── scripts/
│   ├── scripts_for_humaneval/
│   ├── scripts_for_mbpp/
│   └── scripts_for_evalperf/        # 新增EvalPerf脚本
│       ├── memory_evalperf.py
│       ├── cpu_evalperf.py
│       ├── cprofile_evalperf.py
│       ├── compare_models_evalperf.py
│       └── batch_test_evalperf.py
└── result/
    ├── Copilot/
    ├── CodeLlama/
    ├── DeepSeek-Coder/
    └── EvalPerf/                    # 新增EvalPerf结果
        ├── Copilot/
        ├── CodeLlama/
        └── DeepSeek-Coder/
```

#### 2.2 性能测试适配
```python
class EvalPerfTester:
    def __init__(self, dataset_path, pe_input_scale='medium'):
        self.dataset_path = dataset_path
        self.pe_input_scale = pe_input_scale
    
    def run_performance_test(self, generated_code, task_info):
        """运行性能测试"""
        # 1. 准备测试数据
        test_data = self.prepare_test_data(task_info['pe_input'])
        
        # 2. 执行性能测试
        results = {
            'runtime': self.measure_runtime(generated_code, test_data),
            'memory': self.measure_memory(generated_code, test_data),
            'cpu': self.measure_cpu(generated_code, test_data)
        }
        
        # 3. 与基线对比
        baseline_results = self.run_baseline_test(task_info['canonical_solution'], test_data)
        results['performance_ratio'] = self.calculate_ratio(results, baseline_results)
        
        return results
```

### 3. 统计分析增强

#### 3.1 批次测试框架
```python
class BatchTester:
    def __init__(self, n_iterations=20):
        self.n_iterations = n_iterations
    
    def run_batch_test(self, model, task_list):
        """运行批次测试"""
        results = []
        
        for iteration in range(self.n_iterations):
            print(f"Running iteration {iteration + 1}/{self.n_iterations}")
            
            for task in task_list:
                # 生成代码
                generated_code = model.generate(task['prompt'])
                
                # 性能测试
                perf_result = self.run_performance_test(generated_code, task)
                perf_result['iteration'] = iteration
                perf_result['task_id'] = task['task_id']
                
                results.append(perf_result)
        
        return self.analyze_batch_results(results)
    
    def analyze_batch_results(self, results):
        """分析批次测试结果"""
        return {
            'mean_runtime': np.mean([r['runtime'] for r in results]),
            'std_runtime': np.std([r['runtime'] for r in results]),
            'confidence_interval': self.calculate_ci([r['runtime'] for r in results]),
            'performance_distribution': self.analyze_distribution(results)
        }
```

#### 3.2 多维度性能分析
```python
class PerformanceAnalyzer:
    def analyze_model_performance(self, results):
        """多维度性能分析"""
        return {
            'efficiency_score': self.calculate_efficiency_score(results),
            'consistency_score': self.calculate_consistency_score(results),
            'optimization_potential': self.analyze_optimization_potential(results),
            'task_difficulty_correlation': self.analyze_task_difficulty(results)
        }
    
    def compare_with_references(self, generated_results, reference_results):
        """与参考实现对比"""
        return {
            'performance_percentile': self.calculate_percentile(generated_results, reference_results),
            'efficiency_ranking': self.rank_against_references(generated_results, reference_results),
            'improvement_suggestions': self.suggest_improvements(generated_results, reference_results)
        }
```

### 4. COT Prompt对照实现

#### 4.1 Prompt变体设计
```python
class PromptVariants:
    def generate_cot_prompt(self, original_prompt):
        """生成COT格式的prompt"""
        cot_prompt = f"""
{original_prompt}

Please solve this step by step:
1. First, understand the problem requirements
2. Consider the time and space complexity
3. Think about edge cases
4. Implement an efficient solution
5. Optimize for performance

Your solution:
"""
        return cot_prompt
    
    def generate_performance_focused_prompt(self, original_prompt, reference_scores):
        """生成性能导向的prompt"""
        best_score = min(reference_scores)
        perf_prompt = f"""
{original_prompt}

Performance requirement: Your solution should be highly efficient.
The best known solution for this problem achieves a performance score of {best_score:.2f}.
Please implement an optimized solution that minimizes time complexity.

Your solution:
"""
        return perf_prompt
```

### 5. 实施步骤

1. **阶段1**: 完成数据预处理和格式转换 ✅
2. **阶段2**: 创建EvalPerf专用的目录结构和脚本模板
3. **阶段3**: 实现批次测试和统计分析框架
4. **阶段4**: 集成COT prompt对照测试
5. **阶段5**: 实现多模型变体分析和综合报告

### 6. 预期收益

1. **更严格的性能评估**: 使用性能最优的基线进行对比
2. **统计学可靠性**: 通过批次测试获得统计学意义的结果
3. **多维度分析**: 从效率、一致性、优化潜力等多个角度评估
4. **实用价值**: 为代码生成模型的性能优化提供具体指导

这个集成方案充分利用了EvalPerf数据集的性能特性，同时保持了与现有框架的兼容性。
