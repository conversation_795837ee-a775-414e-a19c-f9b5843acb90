

def prime_fib(n: int):
    """
    prime_fib returns n-th number that is a Fibonacci number and it's also prime.
    >>> prime_fib(1)
    2
    >>> prime_fib(2)
    3
    >>> prime_fib(3)
    5
    >>> prime_fib(4)
    13
    >>> prime_fib(5)
    89
    """
    # Your code here
    # create a list to store the Fibonacci numbers
    fib = [1, 2]
    # create a variable to store the index of the Fibonacci numbers
    index = 0
    # use a while loop to iterate through the list
    while True:
        # increase the index by 1
        index += 1
        # append the sum of the last two elements to the list
        fib.append(fib[-1]+fib[-2])
        # if the element is a prime number
        if is_prime(fib[-1]):
            # decrease n by 1
            n -= 1
        # if n is equal to 0, return the element
        if n == 0:
            return fib[-1]


METADATA = {}


def check(candidate):
    assert candidate(1) == 2
    assert candidate(2) == 3
    assert candidate(3) == 5
    assert candidate(4) == 13
    assert candidate(5) == 89
    assert candidate(6) == 233
    assert candidate(7) == 1597
    assert candidate(8) == 28657
    assert candidate(9) == 514229
    assert candidate(10) == 433494437

'''
Standard answer: 
    import math

    def is_prime(p):
        if p < 2:
            return False
        for k in range(2, min(int(math.sqrt(p)) + 1, p - 1)):
            if p % k == 0:
                return False
        return True
    f = [0, 1]
    while True:
        f.append(f[-1] + f[-2])
        if is_prime(f[-1]):
            n -= 1
        if n == 0:
            return f[-1]
'''
check(prime_fib)
