
def is_multiply_prime(a):
    """Write a function that returns true if the given number is the multiplication of 3 prime numbers
    and false otherwise.
    Knowing that (a) is less then 100. 
    Example:
    is_multiply_prime(30) == True
    30 = 2 * 3 * 5
    """
    # Your code here
    # create a list to store the prime numbers
    prime = []
    # use a for loop to iterate through the numbers from 2 to 100
    for i in range(2, 101):
        # create a boolean variable to store whether the number is prime or not
        is_prime = True
        # use a for loop to iterate through the numbers from 2 to i-1
        for j in range(2, i):
            # if i is divisible by j, set is_prime to False
            if i%j == 0:
                is_prime = False
                break
        # if is_prime is True, append i to the list
        if is_prime:
            prime.append(i)
    # use a for loop to iterate through the list
    for i in prime:
        # use a for loop to iterate through the list
        for j in prime:
            # if i is equal to j, skip this iteration
            if i == j:
                continue
            # use a for loop to iterate through the list
            for k in prime:
                # if i is equal to k, skip this iteration
                if i == k:
                    continue
                # if i*j*k is equal to a, return True
                if i*j*k == a:
                    return True
    # otherwise return False
    return False




def check(candidate):

    assert candidate(5) == False
    assert candidate(30) == True
    assert candidate(8) == True
    assert candidate(10) == False
    assert candidate(125) == True
    assert candidate(3 * 5 * 7) == True
    assert candidate(3 * 6 * 7) == False
    assert candidate(9 * 9 * 9) == False
    assert candidate(11 * 9 * 9) == False
    assert candidate(11 * 13 * 7) == True

'''
Standard answer: 
    def is_prime(n):
        for j in range(2,n):
            if n%j == 0:
                return False
        return True

    for i in range(2,101):
        if not is_prime(i): continue
        for j in range(2,101):
            if not is_prime(j): continue
            for k in range(2,101):
                if not is_prime(k): continue
                if i*j*k == a: return True
    return False
'''
check(is_multiply_prime)
