def count_nums(arr):
    """
    Write a function count_nums which takes an array of integers and returns
    the number of elements which has a sum of digits > 0.
    If a number is negative, then its first signed digit will be negative:
    e.g. -123 has signed digits -1, 2, and 3.
    >>> count_nums([]) == 0
    >>> count_nums([-1, 11, -11]) == 1
    >>> count_nums([1, 1, 2]) == 3
    """

    def digits_sum(n):
        neg = 1
        if n < 0:
            (n, neg) = (-1 * n, -1)
        n = [int(i) for i in str(n)]
        n[0] = n[0] * neg
        return sum(n)
    return len(list(filter(lambda x: x > 0, [digits_sum(i) for i in arr])))

def check(candidate):
    for item in range(30):
        assert candidate([]) == 0
        assert candidate([-1, -2, 0]) == 0
        assert candidate([1, 1, 2, -2, 3, 4, 5]) == 6
        assert candidate([1, 6, 9, -6, 0, 1, 5]) == 5
        assert candidate([1, 100, 98, -7, 1, -1]) == 4
        assert candidate([12, 23, 34, -45, -56, 0]) == 5
        assert candidate([-0, 1 ** 0]) == 1
        assert candidate([1]) == 1
        assert True, 'This prints if this assert fails 2 (also good for debugging!)'
check(count_nums)