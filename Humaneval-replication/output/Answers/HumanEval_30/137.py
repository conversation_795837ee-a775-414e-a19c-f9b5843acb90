def compare_one(a, b):
    """
    Create a function that takes integers, floats, or strings representing
    real numbers, and returns the larger variable in its given variable type.
    Return None if the values are equal.
    Note: If a real number is represented as a string, the floating point might be . or ,

    compare_one(1, 2.5) ➞ 2.5
    compare_one(1, "2,3") ➞ "2,3"
    compare_one("5,1", "6") ➞ "6"
    compare_one("1", 1) ➞ None
    """
    (temp_a, temp_b) = (a, b)
    if isinstance(temp_a, str):
        temp_a = temp_a.replace(',', '.')
    if isinstance(temp_b, str):
        temp_b = temp_b.replace(',', '.')
    if float(temp_a) == float(temp_b):
        return None
    return a if float(temp_a) > float(temp_b) else b

def check(candidate):
    for item in range(30):
        assert candidate(1, 2) == 2
        assert candidate(1, 2.5) == 2.5
        assert candidate(2, 3) == 3
        assert candidate(5, 6) == 6
        assert candidate(1, '2,3') == '2,3'
        assert candidate('5,1', '6') == '6'
        assert candidate('1', '2') == '2'
        assert candidate('1', 1) == None
        assert True
check(compare_one)