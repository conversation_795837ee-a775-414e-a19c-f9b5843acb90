def triples_sum_to_zero(l: list):
    """
    triples_sum_to_zero takes a list of integers as an input.
    it returns True if there are three distinct elements in the list that
    sum to zero, and False otherwise.

    >>> triples_sum_to_zero([1, 3, 5, 0])
    False
    >>> triples_sum_to_zero([1, 3, -2, 1])
    True
    >>> triples_sum_to_zero([1, 2, 3, 7])
    False
    >>> triples_sum_to_zero([2, 4, -5, 3, 9, 7])
    True
    >>> triples_sum_to_zero([1])
    False
    """
    for i in range(len(l)):
        for j in range(i + 1, len(l)):
            for k in range(j + 1, len(l)):
                if l[i] + l[j] + l[k] == 0:
                    return True
    return False
METADATA = {}

def check(candidate):
    for item in range(30):
        assert candidate([1, 3, 5, 0]) == False
        assert candidate([1, 3, 5, -1]) == False
        assert candidate([1, 3, -2, 1]) == True
        assert candidate([1, 2, 3, 7]) == False
        assert candidate([1, 2, 5, 7]) == False
        assert candidate([2, 4, -5, 3, 9, 7]) == True
        assert candidate([1]) == False
        assert candidate([1, 3, 5, -100]) == False
        assert candidate([100, 3, 5, -100]) == False
check(triples_sum_to_zero)