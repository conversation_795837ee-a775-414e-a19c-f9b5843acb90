def is_nested(string):
    """
    Create a function that takes a string as input which contains only square brackets.
    The function should return True if and only if there is a valid subsequence of brackets 
    where at least one bracket in the subsequence is nested.

    is_nested('[[]]') ➞ True
    is_nested('[]]]]]]][[[[[]') ➞ False
    is_nested('[][]') ➞ False
    is_nested('[]') ➞ False
    is_nested('[[][]]') ➞ True
    is_nested('[[]][[') ➞ True
    """
    opening_bracket_index = []
    closing_bracket_index = []
    for i in range(len(string)):
        if string[i] == '[':
            opening_bracket_index.append(i)
        else:
            closing_bracket_index.append(i)
    closing_bracket_index.reverse()
    cnt = 0
    i = 0
    l = len(closing_bracket_index)
    for idx in opening_bracket_index:
        if i < l and idx < closing_bracket_index[i]:
            cnt += 1
            i += 1
    return cnt >= 2

def check(candidate):
    for item in range(30):
        assert candidate('[[]]') == True, 'This prints if this assert fails 1 (good for debugging!)'
        assert candidate('[]]]]]]][[[[[]') == False
        assert candidate('[][]') == False
        assert candidate('[]') == False
        assert candidate('[[[[]]]]') == True
        assert candidate('[]]]]]]]]]]') == False
        assert candidate('[][][[]]') == True
        assert candidate('[[]') == False
        assert candidate('[]]') == False
        assert candidate('[[]][[') == True
        assert candidate('[[][]]') == True
        assert candidate('') == False, 'This prints if this assert fails 2 (also good for debugging!)'
        assert candidate('[[[[[[[[') == False
        assert candidate(']]]]]]]]') == False
check(is_nested)