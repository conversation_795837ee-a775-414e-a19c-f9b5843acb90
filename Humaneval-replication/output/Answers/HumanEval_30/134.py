def check_if_last_char_is_a_letter(txt):
    """
    Create a function that returns True if the last character
    of a given string is an alphabetical character and is not
    a part of a word, and False otherwise.
    Note: "word" is a group of characters separated by space.

    Examples:
    check_if_last_char_is_a_letter("apple pie") ➞ False
    check_if_last_char_is_a_letter("apple pi e") ➞ True
    check_if_last_char_is_a_letter("apple pi e ") ➞ False
    check_if_last_char_is_a_letter("") ➞ False 
    """
    check = txt.split(' ')[-1]
    return True if len(check) == 1 and 97 <= ord(check.lower()) <= 122 else False

def check(candidate):
    for item in range(30):
        assert candidate('apple') == False
        assert candidate('apple pi e') == True
        assert candidate('eeeee') == False
        assert candidate('A') == True
        assert candidate('Pumpkin pie ') == False
        assert candidate('Pumpkin pie 1') == False
        assert candidate('') == False
        assert candidate('eeeee e ') == False
        assert candidate('apple pie') == False
        assert candidate('apple pi e ') == False
        assert True
check(check_if_last_char_is_a_letter)