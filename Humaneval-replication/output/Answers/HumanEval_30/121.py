def solution(lst):
    """Given a non-empty list of integers, return the sum of all of the odd elements that are in even positions.
    

    Examples
    solution([5, 8, 7, 1]) ==> 12
    solution([3, 3, 3, 3, 3]) ==> 9
    solution([30, 13, 24, 321]) ==>0
    """
    return sum([x for (idx, x) in enumerate(lst) if idx % 2 == 0 and x % 2 == 1])

def check(candidate):
    for item in range(30):
        assert candidate([5, 8, 7, 1]) == 12
        assert candidate([3, 3, 3, 3, 3]) == 9
        assert candidate([30, 13, 24, 321]) == 0
        assert candidate([5, 9]) == 5
        assert candidate([2, 4, 8]) == 0
        assert candidate([30, 13, 23, 32]) == 23
        assert candidate([3, 13, 2, 9]) == 3
check(solution)