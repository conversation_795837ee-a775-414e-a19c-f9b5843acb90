def minSubArraySum(nums):
    """
    Given an array of integers nums, find the minimum sum of any non-empty sub-array
    of nums.
    Example
    minSubArraySum([2, 3, 4, 1, 2, 4]) == 1
    minSubArraySum([-1, -2, -3]) == -6
    """
    max_sum = 0
    s = 0
    for num in nums:
        s += -num
        if s < 0:
            s = 0
        max_sum = max(s, max_sum)
    if max_sum == 0:
        max_sum = max((-i for i in nums))
    min_sum = -max_sum
    return min_sum

def check(candidate):
    for item in range(30):
        assert candidate([2, 3, 4, 1, 2, 4]) == 1, 'This prints if this assert fails 1 (good for debugging!)'
        assert candidate([-1, -2, -3]) == -6
        assert candidate([-1, -2, -3, 2, -10]) == -14
        assert candidate([-9999999999999999]) == -9999999999999999
        assert candidate([0, 10, 20, 1000000]) == 0
        assert candidate([-1, -2, -3, 10, -5]) == -6
        assert candidate([100, -1, -2, -3, 10, -5]) == -6
        assert candidate([10, 11, 13, 8, 3, 4]) == 3
        assert candidate([100, -33, 32, -1, 0, -2]) == -33
        assert candidate([-10]) == -10, 'This prints if this assert fails 2 (also good for debugging!)'
        assert candidate([7]) == 7
        assert candidate([1, -1]) == -1
check(minSubArraySum)