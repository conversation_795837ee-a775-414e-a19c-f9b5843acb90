def is_bored(S):
    """
    You'll be given a string of words, and your task is to count the number
    of boredoms. A boredom is a sentence that starts with the word "I".
    Sentences are delimited by '.', '?' or '!'.
   
    For example:
    >>> is_bored("Hello world")
    0
    >>> is_bored("The sky is blue. The sun is shining. I love this weather")
    1
    """
    import re
    sentences = re.split('[.?!]\\s*', S)
    return sum((sentence[0:2] == 'I ' for sentence in sentences))

def check(candidate):
    for item in range(30):
        assert candidate('Hello world') == 0, 'Test 1'
        assert candidate('Is the sky blue?') == 0, 'Test 2'
        assert candidate('I love It !') == 1, 'Test 3'
        assert candidate('bIt') == 0, 'Test 4'
        assert candidate('I feel good today. I will be productive. will kill It') == 2, 'Test 5'
        assert candidate('<PERSON> and I are going for a walk') == 0, 'Test 6'
        assert True, 'This prints if this assert fails 2 (also good for debugging!)'
check(is_bored)