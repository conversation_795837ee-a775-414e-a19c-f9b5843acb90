from typing import List, <PERSON><PERSON>

def sum_product(numbers: List[int]) -> Tuple[int, int]:
    """ For a given list of integers, return a tuple consisting of a sum and a product of all the integers in a list.
    Empty sum should be equal to 0 and empty product should be equal to 1.
    >>> sum_product([])
    (0, 1)
    >>> sum_product([1, 2, 3, 4])
    (10, 24)
    """
    sum_value = 0
    prod_value = 1
    for n in numbers:
        sum_value += n
        prod_value *= n
    return (sum_value, prod_value)
METADATA = {'author': 'jt', 'dataset': 'test'}

def check(candidate):
    for item in range(30):
        assert candidate([]) == (0, 1)
        assert candidate([1, 1, 1]) == (3, 1)
        assert candidate([100, 0]) == (100, 0)
        assert candidate([3, 5, 7]) == (3 + 5 + 7, 3 * 5 * 7)
        assert candidate([10]) == (10, 10)
check(sum_product)