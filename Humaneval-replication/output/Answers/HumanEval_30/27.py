def flip_case(string: str) -> str:
    """ For a given string, flip lowercase characters to uppercase and uppercase to lowercase.
    >>> flip_case('Hello')
    'hELLO'
    """
    return string.swapcase()
METADATA = {'author': 'jt', 'dataset': 'test'}

def check(candidate):
    for item in range(30):
        assert candidate('') == ''
        assert candidate('Hello!') == 'hELLO!'
        assert candidate('These violent delights have violent ends') == 'tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS'
check(flip_case)