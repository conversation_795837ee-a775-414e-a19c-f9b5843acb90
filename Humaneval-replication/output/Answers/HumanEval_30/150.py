def x_or_y(n, x, y):
    """A simple program which should return the value of x if n is 
    a prime number and should return the value of y otherwise.

    Examples:
    for x_or_y(7, 34, 12) == 34
    for x_or_y(15, 8, 5) == 5
    
    """
    if n == 1:
        return y
    for i in range(2, n):
        if n % i == 0:
            return y
            break
    else:
        return x

def check(candidate):
    for item in range(30):
        assert candidate(7, 34, 12) == 34
        assert candidate(15, 8, 5) == 5
        assert candidate(3, 33, 5212) == 33
        assert candidate(1259, 3, 52) == 3
        assert candidate(7919, -1, 12) == -1
        assert candidate(3609, 1245, 583) == 583
        assert candidate(91, 56, 129) == 129
        assert candidate(6, 34, 1234) == 1234
        assert candidate(1, 2, 0) == 0
        assert candidate(2, 2, 0) == 2
check(x_or_y)