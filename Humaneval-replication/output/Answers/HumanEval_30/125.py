def split_words(txt):
    """
    Given a string of words, return a list of words split on whitespace, if no whitespaces exists in the text you
    should split on commas ',' if no commas exists you should return the number of lower-case letters with odd order in the
    alphabet, ord('a') = 0, ord('b') = 1, ... ord('z') = 25
    Examples
    split_words("Hello world!") ➞ ["Hello", "world!"]
    split_words("Hello,world!") ➞ ["Hello", "world!"]
    split_words("abcdef") == 3 
    """
    if ' ' in txt:
        return txt.split()
    elif ',' in txt:
        return txt.replace(',', ' ').split()
    else:
        return len([i for i in txt if i.islower() and ord(i) % 2 == 0])

def check(candidate):
    for item in range(30):
        assert candidate('Hello world!') == ['Hello', 'world!']
        assert candidate('Hello,world!') == ['Hello', 'world!']
        assert candidate('Hello world,!') == ['Hello', 'world,!']
        assert candidate('Hello,Hello,world !') == ['Hello,Hello,world', '!']
        assert candidate('abcdef') == 3
        assert candidate('aaabb') == 2
        assert candidate('aaaBb') == 1
        assert candidate('') == 0
check(split_words)