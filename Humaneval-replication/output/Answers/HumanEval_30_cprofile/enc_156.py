from memory_profiler import profile
@profile
def encapsulated_function():
    def int_to_mini_roman(number):
        """
        Given a positive integer, obtain its roman numeral equivalent as a string,
        and return it in lowercase.
        Restrictions: 1 <= num <= 1000
    
        Examples:
        >>> int_to_mini_roman(19) == 'xix'
        >>> int_to_mini_roman(152) == 'clii'
        >>> int_to_mini_roman(426) == 'cdxxvi'
        """
        num = [1, 4, 5, 9, 10, 40, 50, 90, 100, 400, 500, 900, 1000]
        sym = ['I', 'IV', 'V', 'IX', 'X', 'XL', 'L', 'XC', 'C', 'CD', 'D', 'CM', 'M']
        i = 12
        res = ''
        while number:
            div = number // num[i]
            number %= num[i]
            while div:
                res += sym[i]
                div -= 1
            i -= 1
        return res.lower()
    
    def check(candidate):
        for item in range(30):
            assert candidate(19) == 'xix'
            assert candidate(152) == 'clii'
            assert candidate(251) == 'ccli'
            assert candidate(426) == 'cdxxvi'
            assert candidate(500) == 'd'
            assert candidate(1) == 'i'
            assert candidate(4) == 'iv'
            assert candidate(43) == 'xliii'
            assert candidate(90) == 'xc'
            assert candidate(94) == 'xciv'
            assert candidate(532) == 'dxxxii'
            assert candidate(900) == 'cm'
            assert candidate(994) == 'cmxciv'
            assert candidate(1000) == 'm'
            assert True
    check(int_to_mini_roman)
