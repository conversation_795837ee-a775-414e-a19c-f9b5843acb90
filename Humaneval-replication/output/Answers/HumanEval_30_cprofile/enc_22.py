from memory_profiler import profile
@profile
def encapsulated_function():
    from typing import List, Any
    
    def filter_integers(values: List[Any]) -> List[int]:
        """ Filter given list of any python values only for integers
        >>> filter_integers(['a', 3.14, 5])
        [5]
        >>> filter_integers([1, 2, 3, 'abc', {}, []])
        [1, 2, 3]
        """
        return [x for x in values if isinstance(x, int)]
    METADATA = {'author': 'jt', 'dataset': 'test'}
    
    def check(candidate):
        for item in range(30):
            assert candidate([]) == []
            assert candidate([4, {}, [], 23.2, 9, 'adasd']) == [4, 9]
            assert candidate([3, 'c', 3, 3, 'a', 'b']) == [3, 3, 3]
    check(filter_integers)
