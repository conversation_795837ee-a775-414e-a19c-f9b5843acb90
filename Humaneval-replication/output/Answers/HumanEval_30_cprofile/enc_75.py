from memory_profiler import profile
@profile
def encapsulated_function():
    def is_multiply_prime(a):
        """Write a function that returns true if the given number is the multiplication of 3 prime numbers
        and false otherwise.
        Knowing that (a) is less then 100. 
        Example:
        is_multiply_prime(30) == True
        30 = 2 * 3 * 5
        """
    
        def is_prime(n):
            for j in range(2, n):
                if n % j == 0:
                    return False
            return True
        for i in range(2, 101):
            if not is_prime(i):
                continue
            for j in range(2, 101):
                if not is_prime(j):
                    continue
                for k in range(2, 101):
                    if not is_prime(k):
                        continue
                    if i * j * k == a:
                        return True
        return False
    
    def check(candidate):
        for item in range(30):
            assert candidate(5) == False
            assert candidate(30) == True
            assert candidate(8) == True
            assert candidate(10) == False
            assert candidate(125) == True
            assert candidate(3 * 5 * 7) == True
            assert candidate(3 * 6 * 7) == False
            assert candidate(9 * 9 * 9) == False
            assert candidate(11 * 9 * 9) == False
            assert candidate(11 * 13 * 7) == True
    check(is_multiply_prime)
