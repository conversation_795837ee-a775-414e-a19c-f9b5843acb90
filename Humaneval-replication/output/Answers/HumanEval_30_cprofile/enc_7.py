from memory_profiler import profile
@profile
def encapsulated_function():
    from typing import List
    
    def filter_by_substring(strings: List[str], substring: str) -> List[str]:
        """ Filter an input list of strings only for ones that contain given substring
        >>> filter_by_substring([], 'a')
        []
        >>> filter_by_substring(['abc', 'bacd', 'cde', 'array'], 'a')
        ['abc', 'bacd', 'array']
        """
        return [x for x in strings if substring in x]
    METADATA = {'author': 'jt', 'dataset': 'test'}
    
    def check(candidate):
        for item in range(30):
            assert candidate([], 'john') == []
            assert candidate(['xxx', 'asd', 'xxy', 'john doe', 'xxxAAA', 'xxx'], 'xxx') == ['xxx', 'xxxAAA', 'xxx']
            assert candidate(['xxx', 'asd', 'aaaxxy', 'john doe', 'xxxAA<PERSON>', 'xxx'], 'xx') == ['xxx', 'aaaxxy', 'xxxAAA', 'xxx']
            assert candidate(['grunt', 'trumpet', 'prune', 'gruesome'], 'run') == ['grunt', 'prune']
    check(filter_by_substring)
