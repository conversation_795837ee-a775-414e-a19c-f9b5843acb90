from memory_profiler import profile
@profile
def encapsulated_function():
    def strlen(string: str) -> int:
        """ Return length of given string
        >>> strlen('')
        0
        >>> strlen('abc')
        3
        """
        return len(string)
    METADATA = {'author': 'jt', 'dataset': 'test'}
    
    def check(candidate):
        for item in range(30):
            assert candidate('') == 0
            assert candidate('x') == 1
            assert candidate('asdasnakj') == 9
    check(strlen)
