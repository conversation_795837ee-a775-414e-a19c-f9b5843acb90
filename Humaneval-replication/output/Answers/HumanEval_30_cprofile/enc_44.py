from memory_profiler import profile
@profile
def encapsulated_function():
    def change_base(x: int, base: int):
        """Change numerical base of input number x to base.
        return string representation after the conversion.
        base numbers are less than 10.
        >>> change_base(8, 3)
        '22'
        >>> change_base(8, 2)
        '1000'
        >>> change_base(7, 2)
        '111'
        """
        ret = ''
        while x > 0:
            ret = str(x % base) + ret
            x //= base
        return ret
    METADATA = {}
    
    def check(candidate):
        for item in range(30):
            assert candidate(8, 3) == '22'
            assert candidate(9, 3) == '100'
            assert candidate(234, 2) == '11101010'
            assert candidate(16, 2) == '10000'
            assert candidate(8, 2) == '1000'
            assert candidate(7, 2) == '111'
            for x in range(2, 8):
                assert candidate(x, x + 1) == str(x)
    check(change_base)
