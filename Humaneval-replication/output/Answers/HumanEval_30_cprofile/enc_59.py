from memory_profiler import profile
@profile
def encapsulated_function():
    def largest_prime_factor(n: int):
        """Return the largest prime factor of n. Assume n > 1 and is not a prime.
        >>> largest_prime_factor(13195)
        29
        >>> largest_prime_factor(2048)
        2
        """
    
        def is_prime(k):
            if k < 2:
                return False
            for i in range(2, k - 1):
                if k % i == 0:
                    return False
            return True
        largest = 1
        for j in range(2, n + 1):
            if n % j == 0 and is_prime(j):
                largest = max(largest, j)
        return largest
    METADATA = {}
    
    def check(candidate):
        for item in range(30):
            assert candidate(15) == 5
            assert candidate(27) == 3
            assert candidate(63) == 7
            assert candidate(330) == 11
            assert candidate(13195) == 29
    check(largest_prime_factor)
