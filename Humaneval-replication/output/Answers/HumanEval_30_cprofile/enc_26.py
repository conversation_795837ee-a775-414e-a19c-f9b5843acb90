from memory_profiler import profile
@profile
def encapsulated_function():
    from typing import List
    
    def remove_duplicates(numbers: List[int]) -> List[int]:
        """ From a list of integers, remove all elements that occur more than once.
        Keep order of elements left the same as in the input.
        >>> remove_duplicates([1, 2, 3, 2, 4])
        [1, 3, 4]
        """
        import collections
        c = collections.Counter(numbers)
        return [n for n in numbers if c[n] <= 1]
    METADATA = {'author': 'jt', 'dataset': 'test'}
    
    def check(candidate):
        for item in range(30):
            assert candidate([]) == []
            assert candidate([1, 2, 3, 4]) == [1, 2, 3, 4]
            assert candidate([1, 2, 3, 2, 4, 3, 5]) == [1, 4, 5]
    check(remove_duplicates)
