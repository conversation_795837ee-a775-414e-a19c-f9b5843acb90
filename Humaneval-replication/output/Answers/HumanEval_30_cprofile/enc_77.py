from memory_profiler import profile
@profile
def encapsulated_function():
    def iscube(a):
        """
        Write a function that takes an integer a and returns True 
        if this ingeger is a cube of some integer number.
        Note: you may assume the input is always valid.
        Examples:
        iscube(1) ==> True
        iscube(2) ==> False
        iscube(-1) ==> True
        iscube(64) ==> True
        iscube(0) ==> True
        iscube(180) ==> False
        """
        a = abs(a)
        return int(round(a ** (1.0 / 3))) ** 3 == a
    
    def check(candidate):
        for item in range(30):
            assert candidate(1) == True, 'First test error: ' + str(candidate(1))
            assert candidate(2) == False, 'Second test error: ' + str(candidate(2))
            assert candidate(-1) == True, 'Third test error: ' + str(candidate(-1))
            assert candidate(64) == True, 'Fourth test error: ' + str(candidate(64))
            assert candidate(180) == False, 'Fifth test error: ' + str(candidate(180))
            assert candidate(1000) == True, 'Sixth test error: ' + str(candidate(1000))
            assert candidate(0) == True, '1st edge test error: ' + str(candidate(0))
            assert candidate(1729) == False, '2nd edge test error: ' + str(candidate(1728))
    check(iscube)
